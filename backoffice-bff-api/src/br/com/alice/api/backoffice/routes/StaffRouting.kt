package br.com.alice.api.backoffice.routes

import br.com.alice.api.backoffice.controllers.staff.StaffController
import br.com.alice.common.coHandler
import br.com.alice.common.multipartHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.staffRoutes() {

    authenticate {
        val staffController by inject<StaffController>()
        route("staff") {
            get("/") {
                coHandler(staffController::index)
            }
            post("/") {
                coHandler(staffController::create)
            }
            get("/{id}") {
                coHandler("id", staffController::getById)
            }
            put("/{id}") {
                coHandler("id", staffController::update)
            }
            get("/build_staff") {
                coHandler(staffController::buildFormByStaffType)
            }
            get("/search_provider_units") {
                coHandler(staffController::searchProviderUnits)
            }
            get("/sub_specialties") {
                coHandler(staffController::getSubSpecialty)
            }
            get("/address/search") {
                coHandler(staffController::autocompleteAddress)
            }
            get("/address/{place_id}") {
                coHandler("place_id", staffController::getByPlaceId)
            }
            post("/upload_profile_image") {
                multipartHandler(staffController::uploadProfileImage)
            }
            get("/roles") {
                coHandler(staffController::getStaffRoles)
            }
        }
    }
}
