package br.com.alice.api.backoffice.mappers.staff

import br.com.alice.common.mappers.CommonInputMapper
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.Role
import io.ktor.http.Parameters
import java.util.UUID

object StaffFormInputMapper {
    fun getStaffType(queryParams: Parameters): StaffType =
        CommonInputMapper.getFilterParams<String>(queryParams, "type")
            ?.let { StaffType.valueOf(it) }
            ?: throw IllegalArgumentException("Staff type is required")

    data class StaffFilter(
        val searchToken: String?
    )

    data class StaffSearchFilters(
        val namePrefix: String? = null,
        val active: Boolean? = null,
        val types: List<StaffType>? = null,
        val roles: List<Role>? = null,
        val ids: List<UUID>? = null
    )

    fun toFilter(queryParams: Parameters): StaffFilter {
        val searchToken = CommonInputMapper.getFilterParams<String>(queryParams, "q")
        return StaffFilter(searchToken = searchToken)
    }

    fun toSearchFilters(queryParams: Parameters): StaffSearchFilters {
        val namePrefix = CommonInputMapper.getFilterParams<String>(queryParams, "q")
            ?: CommonInputMapper.getFilterParams<String>(queryParams, "nome")
        val active = CommonInputMapper.getFilterParams<String>(queryParams, "active")?.toBooleanStrictOrNull()
        val typesString = CommonInputMapper.getFilterParams<String>(queryParams, "types")
        val rolesString = CommonInputMapper.getFilterParams<String>(queryParams, "roles")

        val types = typesString?.split(",")?.mapNotNull {
            try { StaffType.valueOf(it.trim()) } catch (e: Exception) { null }
        }

        val roles = rolesString?.split(",")?.mapNotNull {
            try { Role.valueOf(it.trim()) } catch (e: Exception) { null }
        }

        return StaffSearchFilters(
            namePrefix = namePrefix,
            active = active,
            types = types,
            roles = roles,
            ids = null
        )
    }
}