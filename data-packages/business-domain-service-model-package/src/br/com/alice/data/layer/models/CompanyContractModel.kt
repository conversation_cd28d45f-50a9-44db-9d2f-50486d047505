package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CompanyContractModel(
    override val id: UUID = RangeUUID.generate(),
    val externalId: String? = null,
    val title: String,
    val billingAccountablePartyId: UUID? = null,
    val startedAt: LocalDate? = null,
    val accountableEmail: String,
    val contractFileIds: List<ContractFileModel>,
    val isProRata: Boolean? = null,
    val defaultProductId: UUID? = null,
    val availableProducts: List<UUID>? = null,
    val dueDate: Int = 10,
    val isBillingLevel: Boolean = false,
    val groupCompany: String? = null,
    val paymentType: PaymentModel? = null,
    val termStartType: TermStartType? = null,
    override val version: Int = 0,
    val nature: String? = null,
    val status: CompanyContractStatus? = null,
    val integrationStatus: CompanyContractIntegrationStatus? = null,
    val hasRetroactiveCharge: Boolean? = null,
    val beneficiaryCountAtDayZero: Int? = null,
    val companySize: CompanySize? = null,
    val companyBusinessUnit: CompanyBusinessUnit? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    override var updatedBy: UpdatedBy? = null,
) : Model, UpdatedByReference

data class ContractFileModel(
    val id: UUID,
    val type: ContractType,
)
