package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CompanyContract(
    override val id: UUID = RangeUUID.generate(),
    val externalId: String? = null,
    val title: String,
    val billingAccountablePartyId: UUID? = null,
    val startedAt: LocalDate? = null,
    val accountableEmail: String,
    val contractFileIds: List<ContractFile>,
    val isProRata: Boolean? = null,
    val defaultProductId: UUID? = null,
    val availableProducts: List<UUID>? = null,
    val dueDate: Int = 10,
    val isBillingLevel: Boolean = false,
    val groupCompany: String? = null,
    val paymentType: PaymentModel? = null,
    val termStartType: TermStartType? = null,
    override val version: Int = 0,
    val nature: String? = null,
    val status: CompanyContractStatus? = null,
    val integrationStatus: CompanyContractIntegrationStatus? = null,
    val hasRetroactiveCharge: Boolean? = null,
    val beneficiaryCountAtDayZero: Int? = null,
    val companySize: CompanySize? = null,
    val companyBusinessUnit: CompanyBusinessUnit? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    override var updatedBy: UpdatedBy? = null,
) : Model, UpdatedByReference

enum class TermStartType {
    FIXED, FLEXIBLE
}

data class ContractFile(
    val id: UUID,
    val type: ContractType,
)

enum class ContractType(description: String) {
    MAIN("Principal"),
    AMENDMENT("Aditivo"),
}

enum class PaymentModel {
    PRE_PAY,
    POST_PAY,
}

enum class CompanyContractStatus(val description: String) {
    ACTIVE("Ativo na TOTVS"),
    DRAFT("Rascunho"),

    @Deprecated("Use o CompanyContractIntegrationStatus.PROCESSING para representar a integração")
    PROCESSING("Em Processamento"),

    @Deprecated("Use o CompanyContractIntegrationStatus.FAILED para representar a integração")
    FAILED("Falha ao inserir na TOTVS"),
    BLOCKED("Bloqueado"),
    CANCELED("Cancelado"),
}

enum class CompanyContractIntegrationStatus(val description: String) {
    PROCESSING("Em Processamento"),
    FAILED("Falha ao inserir na TOTVS"),
    PROCESSED("Ativo na TOTVS"),
}

enum class CompanySize {
    MICRO,
    SMALL,
    MLA,
    UNKNOWN
}

fun getCompanySizeByBeneficiaryCount(beneficiaryCount: Int): CompanySize? =
    when {
        beneficiaryCount == 0 -> CompanySize.UNKNOWN
        beneficiaryCount <= 5 -> CompanySize.MICRO
        beneficiaryCount <= 29 -> CompanySize.SMALL
        else -> CompanySize.MLA
    }
