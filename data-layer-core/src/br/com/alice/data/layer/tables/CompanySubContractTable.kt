package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.CompanySubContractIntegrationStatus
import br.com.alice.data.layer.models.CompanySubContractStatus
import br.com.alice.data.layer.models.PaymentModel
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class CompanySubContractTable(
    override val id: UUID = RangeUUID.generate(),
    val externalId: String? = null,
    val title: String,
    val billingAccountablePartyId: UUID? = null,
    val companyId: UUID,
    val contractId: UUID,
    val flexBenefit: Boolean? = null,
    val hasEmployeesAbroad: Boolean = true,
    val defaultFlowType: BeneficiaryOnboardingFlowType? = null,
    val isProRata: Boolean? = true,
    val defaultProductId: UUID? = null,
    val availableProducts: List<UUID>? = null,
    val dueDate: Int = 10,
    val isBillingLevel: Boolean = false,
    val paymentType: PaymentModel? = null,
    val nature: String? = null,
    val billingGroup: String? = null,
    val avaliableCompanyProductPriceListing: List<UUID>? = null,
    val status: CompanySubContractStatus? = null,
    val integrationStatus: CompanySubContractIntegrationStatus? = null,
    val hasRetroactiveCharge: Boolean? = null,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val blockedAt: LocalDate? = null,
    val updatedBy: UpdatedBy? = null
) : Table<CompanySubContractTable> {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
