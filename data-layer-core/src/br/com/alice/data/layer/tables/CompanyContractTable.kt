package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.CompanyBusinessUnit
import br.com.alice.data.layer.models.CompanyContractIntegrationStatus
import br.com.alice.data.layer.models.CompanyContractStatus
import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.ContractFileModel
import br.com.alice.data.layer.models.PaymentModel
import br.com.alice.data.layer.models.TermStartType
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class CompanyContractTable(
    override val id: UUID = RangeUUID.generate(),
    val externalId: String? = null,
    val title: String,
    val billingAccountablePartyId: UUID? = null,
    val startedAt: LocalDate? = null,
    val accountableEmail: String,
    val contractFileIds: List<ContractFileModel>,
    val isProRata: Boolean? = null,
    val defaultProductId: UUID? = null,
    val availableProducts: List<UUID>? = null,
    val dueDate: Int = 10,
    val isBillingLevel: Boolean = false,
    val groupCompany: String? = null,
    val paymentType: PaymentModel? = null,
    val termStartType: TermStartType? = null,
    val nature: String? = null,
    val status: CompanyContractStatus? = null,
    val integrationStatus: CompanyContractIntegrationStatus? = null,
    val hasRetroactiveCharge: Boolean? = null,
    val beneficiaryCountAtDayZero: Int? = null,
    val companySize: CompanySize? = null,
    val companyBusinessUnit: CompanyBusinessUnit? = null,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy? = null
) : Table<CompanyContractTable> {
    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
}
