package br.com.alice.api.event

import br.com.alice.api.event.ServiceConfig.ALICE_SECRET_KEY
import br.com.alice.api.event.services.TOKEN_ISSUER
import br.com.alice.common.service.serialization.gsonCompleteSerializer
import br.com.alice.featureconfig.core.FeaturePopulateService
import com.typesafe.config.ConfigFactory
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.SignatureAlgorithm
import io.jsonwebtoken.io.Encoders
import io.jsonwebtoken.security.Keys
import io.ktor.client.request.header
import io.ktor.client.request.request
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpMethod.Companion.Delete
import io.ktor.http.HttpMethod.Companion.Get
import io.ktor.http.HttpMethod.Companion.Post
import io.ktor.http.HttpMethod.Companion.Put
import io.ktor.server.application.Application
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.testing.testApplication
import io.mockk.mockk
import org.koin.core.context.loadKoinModules
import org.koin.core.context.stopKoin
import org.koin.dsl.module
import java.util.Calendar
import kotlin.test.AfterTest
import kotlin.test.BeforeTest

abstract class ControllerTestHelper {

    private val featurePopulateService: FeaturePopulateService = mockk()

    protected open val module = module {
        single { featurePopulateService }
        single { HoconApplicationConfig(ConfigFactory.load("application.conf")) }
    }

    protected open val moduleFunction: Application.() -> Unit = { loadKoinModules(module) }
    private var authenticationToken = "xxx"

    private val clientIdTest = "f374df57-2a1f-489a-8abc-0cdb8fbedd5f"
    protected val token = generateTokenForTest()

    @BeforeTest
    open fun setup() = stopKoin()

    @AfterTest
    open fun after() = stopKoin()

    fun get(url: String, headers: Map<String, String> = mapOf(), assertFunction: suspend (HttpResponse) -> Unit) =
        handleRequest(Get, url, null, headers, assertFunction)

    fun post(
        url: String,
        body: Any? = null,
        headers: Map<String, String> = mapOf(),
        assertFunction: suspend (HttpResponse) -> Unit
    ) =
        handleRequest(Post, url, body, headers, assertFunction)

    fun put(
        url: String,
        body: Any? = null,
        headers: Map<String, String> = mapOf(),
        assertFunction: suspend (HttpResponse) -> Unit
    ) =
        handleRequest(Put, url, body, headers, assertFunction)

    fun delete(url: String, headers: Map<String, String> = mapOf(), assertFunction: suspend (HttpResponse) -> Unit) =
        handleRequest(Delete, url, null, headers, assertFunction)

    private fun handleRequest(
        method: HttpMethod,
        to: String,
        body: Any?,
        headers: Map<String, String>,
        assertFunction: suspend (HttpResponse) -> Unit
    ) = testApplication {
        application(moduleFunction)

        client.request(to) {
            this.method = method

            header(HttpHeaders.Authorization, authenticationToken)
            header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
            header(HttpHeaders.AcceptLanguage, "pt-BR")
            headers.entries.forEach { header(it.key, it.value) }
            body?.let { if (it is String) setBody(it) else setBody(gsonCompleteSerializer.toJson(it)) }
        }.apply { assertFunction(this) }
    }

    fun authenticated(function: () -> Unit) {
        authenticationToken = ALICE_SECRET_KEY
        function()
    }

    fun netLexAuthenticated(token: String, function: () -> Unit) {
        authenticationToken = token
        function()
    }

    private fun generateTokenForTest(id: String = clientIdTest): String {
        val signatureAlgorithm: SignatureAlgorithm = SignatureAlgorithm.HS256
        val now = Calendar.getInstance().time
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.HOUR, 1)

        val key = Keys.hmacShaKeyFor(
            Encoders.BASE64URL.encode(ServiceConfig.secretKey()).toByteArray()
        )

        val builder = Jwts.builder().setId(id)
            .setIssuedAt(now)
            .setIssuer(TOKEN_ISSUER)
            .setExpiration(calendar.time)
            .signWith(key, signatureAlgorithm)

        return builder.compact()
    }
}
