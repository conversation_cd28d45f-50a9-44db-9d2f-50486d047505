package br.com.alice.api.event.routes

import br.com.alice.api.event.controllers.NetLexController
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Route
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Route.netLexRoutes() {
    val netLexController by inject<NetLexController>()
    post("/auth/netlex") {
        coHandler(netLexController::authorize)
    }

    authenticate("auth-netlex") {
        route("/netlex") {
            post("/health_professionals") {
                coHandler(netLexController::createHealthProfessional)
            }
        }
    }
}
