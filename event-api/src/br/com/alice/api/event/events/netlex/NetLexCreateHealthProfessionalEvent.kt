package br.com.alice.api.event.events.netlex

import br.com.alice.api.event.SERVICE_NAME
import br.com.alice.api.event.model.NetLexHealthProfessionalRequest
import br.com.alice.common.notification.NotificationEvent

class NetLexCreateHealthProfessionalEvent(
    data: NetLexHealthProfessionalRequest
) : NotificationEvent<NetLexHealthProfessionalRequest>(
    name = name,
    producer = SERVICE_NAME,
    payload = data
) {
    companion object {
        const val name = "netlex-create-health-professional"
    }
}
