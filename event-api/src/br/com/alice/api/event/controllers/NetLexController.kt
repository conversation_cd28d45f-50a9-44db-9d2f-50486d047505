package br.com.alice.api.event.controllers

import br.com.alice.api.event.events.netlex.NetLexCreateHealthProfessionalEvent
import br.com.alice.api.event.model.NetLexHealthProfessionalRequest
import br.com.alice.api.event.services.NetLexAuthService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import io.ktor.http.HttpStatusCode

class NetLexController(
    private val authService: NetLexAuthService,
    private val kafkaProducerService: KafkaProducerService
) : Controller() {
    fun authorize(request: AuthRequest): Response =
        authService.authorize(request.clientId, request.clientSecret)
            ?.let { Response(HttpStatusCode.OK, AuthResponse(it)) }
            ?: Response(HttpStatusCode.Unauthorized)

    suspend fun createHealthProfessional(request: NetLexHealthProfessionalRequest): Response =
        try {
            kafkaProducerService.produce(NetLexCreateHealthProfessionalEvent(request))
            Response.OK
        } catch (ex: Exception) {
            logger.error("Error creating Health Professional", ex)
            Response(HttpStatusCode.InternalServerError, mapOf("error" to "Failed to create Health Professional"))
        }

}

data class AuthRequest(
    val clientId: String,
    val clientSecret: String
)

data class AuthResponse(
    val token: String
)
